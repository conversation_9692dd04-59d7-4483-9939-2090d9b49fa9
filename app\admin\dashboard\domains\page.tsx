"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'


export default function AdminDomainsPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the unified domain management page
    router.replace('/admin/dashboard/tenants/domains')
  }, [router])

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
        <span className="ml-2 text-gray-600">Redirecting to unified domain management...</span>
      </div>
    </div>
  )
}
